import { ICrmLookupLocalized } from "interfaces/CrmMasterData.interface";

export const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL;

export const NEXT_AUTH_ENABLED = true;
export const NEXT_AUTH_SESSION_MAX_AGE = 3 * 60 * 60;
export const INFLATION_TYPE = 8;

export const EMIRATES_ID_REGEX = /^784-?[0-9]{4}-?[0-9]{7}-?[0-9]{1}$/;
export const URL_REGEX =
	/(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s()]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s()]{2,}[^\s.)(])|(smartservices\.mocd\.gov\.ae)|(mocd\.gov\.ae)/gi;

export const UAEPASS_AUTH_API = process.env.UAEPASS_AUTH_API;
export const UAEPASS_TOKEN_API = process.env.UAEPASS_TOKEN_API;
export const UAEPASS_USERINFO_API = process.env.UAEPASS_USERINFO_API;
export const UAEPASS_SIGNOUT_URL = process.env.UAEPASS_SIGNOUT_URL;
export const UAEPASS_CLIENT_ID = process.env.UAEPASS_CLIENT_ID;
export const UAEPASS_CLIENT_SECRET = process.env.UAEPASS_CLIENT_SECRET;
export const UNEMPLOYED_ID = process.env.NEXT_PUBLIC_UNEMPLOYED_ID;
export const EMPLOYED_ID = "b2d1f47d-c36c-ed11-81ac-0022480da504";
export const farmerServiceSocialEntites: ICrmLookupLocalized[] = [
	{ label: "mocd", value: 1 },
	{ label: "docd-ad", value: 2 },
	{ label: "docd-dubait", value: 3 },
	{ label: "socialServices-sharjah", value: 4 },
];
export const WRAPPER_API_ENDPOINT_URL = process.env.WRAPPER_API_ENDPOINT_URL;
export const CONNECTION_STRING = process.env.NEXT_PUBLIC_CONNECTION_STRING;

export const OTP_RESEND_TIMEOUT = 60 * 1000;

export const SHOW_MOCK_LOGIN = process.env.NODE_ENV === "development";

export const STATUS_SLUG_MAPPING = {
	"07ddf599-8278-ed11-81ad-0022480da237": "pending",
	"d82854f3-c46c-ed11-81ac-0022480da504": "submitted",
	"e3e86c3a-a66c-ed11-81ac-0022480da504": "requestRejected",
	"ef2c8fa5-a26c-ed11-81ac-0022480da504": "requestApproved",
	"aa414ce7-c46c-ed11-81ac-0022480da504": "draft",
	"bb052d0c-1747-ee11-be6f-6045bd6a5068": "temporaryQueue",
	"3d14c587-77a3-ee11-be36-000d3a6c23a9": "PendingRefundApproved",
	"e4b7fdd6-77a3-ee11-be36-000d3a6c23a9": "PendingRefundNotApproved",
	"662410004": "OverduePayment",
	"2": "PaymentCompleted",
	"662410007": "PaymentCompleted",
	"662410005": "PaymentAccumulated",
	"662410003": "PendingConfirmation",
	"662410000": "PendingPayment",
	"662410006": "Stopped",
};
export const PAYMENT_OPTIONS_MAPPING = {
	"e04d342d-a699-ee11-be37-6045bd6972e9": "5%",
	"d614cd81-599a-ee11-be37-6045bd6a5296": "10%",
	"9577d669-599a-ee11-be37-6045bd6a5296": "15%",
	"2298e826-a699-ee11-be37-6045bd6972e9": "20%",
	"a44d342d-a699-ee11-be37-6045bd6972e9": "25%",
	"bc2df020-a699-ee11-be37-6045bd6972e9": "100%",
};

export const EDIT_REASON_NOMINATED_CASES = "ce961e4c-a9a8-ef11-b8e8-0022486a373d";
export const STOP_REASON_NOMINATED_CASES = "20882a1d-5a94-f011-b112-005056010908";
export const ADD_REASON_NOMINATED_CASES = "fe1aa7f7-5994-f011-b112-005056010908";

export const DRAFT_STATUS_ID = "aa414ce7-c46c-ed11-81ac-0022480da504";
export const SOCIALAID_TEMPLATE_ID = "abd2d86d-4a75-4c6d-8156-2ba0b523942f";
export const INFLATION_TEMPLATE_ID = "94b2a9e5-d2ae-ee11-a568-000d3a6c23a9";
export const INFLATION_TEMPLATE_ID_2 = "c1b5dedd-62e8-40a6-9a3a-997355dda8ec";
export const PENDING_STATUS_ID = "07ddf599-8278-ed11-81ad-0022480da237";

export const WIFE_LOOKUP_ID = "4bfd9b8d-6a75-ed11-81ad-002248cbd873";
export const HUSBAND_LOOKUP_ID = "6dfd9b8d-6a75-ed11-81ad-002248cbd873";
export const DAUGHTER_LOOKUP_ID = "4ffd9b8d-6a75-ed11-81ad-002248cbd873";
export const SISTER_LOOKIP_ID = "55fd9b8d-6a75-ed11-81ad-002248cbd873";
export const BROTHER_LOOKUP_ID = "53fd9b8d-6a75-ed11-81ad-002248cbd873";
export const SON_LOOKUP_ID = "4dfd9b8d-6a75-ed11-81ad-002248cbd873";
export const UNDER_45_AGE = "ff3db27d-9257-ee11-be6f-6045bd14ccdc";
export const DIVORCED = "0b3eb27d-9257-ee11-be6f-6045bd14ccdc";
export const BORN_UNKNOWN_PARENTS = "1c3eb27d-9257-ee11-be6f-6045bd14ccdc";
export const CHILD_OF_PRISONER = "1a3eb27d-9257-ee11-be6f-6045bd14ccdc";
export const ORPHANS_ONLY = "183eb27d-9257-ee11-be6f-6045bd14ccdc";
export const CHILD_IN_DIFFICULT_SITUATION = "15a6a54f-9257-ee11-be6f-6045bd6aa1f5";
export const SPOUSE_INCAPACITATED_FOREIGNER = "163eb27d-9257-ee11-be6f-6045bd14ccdc";
export const ABANDONED = "143eb27d-9257-ee11-be6f-6045bd14ccdc";
export const WIDOWED = "123eb27d-9257-ee11-be6f-6045bd14ccdc";
export const SPOUSE_OF_PRISONER = "0d3eb27d-9257-ee11-be6f-6045bd14ccdc";
export const COMPLAINT_TYPE = "1";
export const INQUIRY_TYPE = "2";
export const EMPLOYEDLOWINCOME = "09a6a54f-9257-ee11-be6f-6045bd6aa1f5";
export const NEW_CATEGORY_ID = "0ba6a54f-9257-ee11-be6f-6045bd6aa1f5";
export const DIVORCED_WOMAN_ABOVE_45 = "cdbc97cd-2294-ee11-be37-6045bd6a5296";
export const POD_CHILD_SUBCATEGORY_ID = "093eb27d-9257-ee11-be6f-6045bd14ccdc";

export const CHILD_LIST = [SON_LOOKUP_ID, BROTHER_LOOKUP_ID, SISTER_LOOKIP_ID, DAUGHTER_LOOKUP_ID];

export const PROCESS_TEMPLATE_ID = "abd2d86d-4a75-4c6d-8156-2ba0b523942f";
export const TESTING_PROCESS_TEMPLATE_ID = "abd2d86d-4a75-4c6d-8156-2ba0b523942f";
export const ALLOWANCE_CATEGORY = "51211E5E-9D6C-ED11-81AC-0022480DA504";

export const FARMER_PROCESS_TEMPLATE = "977cf93a-e2ef-ed11-8849-6045bd6a528f";
export const MARRIED_MARITAL_STATUS_ID = "38422b1b-6d75-ed11-81ad-0022480dc264";
export const NOTIFICATION_REFETCH_INTERVAL = 5 * 60 * 1000;
export const NOTIFICATION_MARK_READ_INTERVAL = 5 * 1000;

export const DUMMY_WIFE_RECORD_ID = "_dummynewaddedWifeRecord";
export const DUMMY_HUSBAND_RECORD_ID = "_dummynewaddedHusbandRecord";

export const CONTACT_US_EMAIL = "<EMAIL>";

export const PDF_MIME_TYPE = ["application/pdf"];
export const PDF_EXTENSION = [".pdf"];
export const IMAGE_MIME_TYPE = ["image/jpeg", "image/jpg", "image/png", "image/heif", "image/heic"];
export const IMAGE_EXTENSION = [".jpg", ".jpeg", ".png", ".heif", ".heic"];
export const IMAGE_VISIBLE_EXTENSION = [".jpg", ".png"];
export const MAX_FILE_SIZE_MB = 5;
export const MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024;

export const FEEDBACK_TITLE = "Feedback for Social Welfare Service";
export const TCS_PDF_FILE_LOCATION_AR = "/assets/pdf/termsAndConditions_ar.pdf";
export const TCS_PDF_FILE_LOCATION_EN = "/assets/pdf/termsAndConditions_en.pdf";
export const TCS_PDF_FILE_LOCATION_AR_FARMER = "/assets/pdf/termsAndConditions-ar-farmer.pdf";

export const IS_OPERATIONS = process.env.NEXT_PUBLIC_IS_OPERATIONS === "true";
export const LANG_SWITCHER_ENABLED = true;

export const CertificateTypes = {
	Own: 1,
	Family: 2,
};

export const EntityAddressed = {
	ICP: 1,
	Other: 2,
	ToWhom: 3,
};

export const ComplaintCaseTypes = {
	Complaint: 1,
	Inquiry: 2,
	Suggestion: 662410000,
	RequestService: 3,
};

export const Active = {
	InProgree: 1,
};

export const Resolved = {
	ProblemSolved: 5,
};

export const Terminated = [
	{
		value: "1",
		label: "I have been terminated from Public/Semi-Government",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "2",
		label: "I have been terminated from private sector",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "3",
		label: "I did not work before",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const TerminatedAr = [
	{
		value: "1",
		label: "تم انهاء خدمتي من جهة حكومية او جهة شبه حكومية ",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "2",
		label: "تم انهاء خدمتي من القطاع الخاص",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "3",
		label: "لم اعمل سابقاً",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];
export const MilitaryServiceStatus = [
	{
		value: "1",
		label: "Enrolled in Military service",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "2",
		label: "Completed Military service",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "3",
		label: "Exempted from Military service",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "4",
		label: "Not applicable for women",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const MilitaryServiceStatusAr = [
	{
		value: "1",
		label: "مستمر بالخدمة الوطنية",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "2",
		label: "تم اكمال الخدمة الوطنية ",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "3",
		label: "معفي من الخدمة الوطنية",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "4",
		label: "لا ينطبق لللاناث",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const ChildEligibilityforWomeninDifficultyAr = [
	{
		value: "662410003",
		label: "لا ينطبق",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410000",
		label: "طفل واحد على الأقل أقل من 4 سنوات",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410001",
		label: "طفل واحد على الأقل من ذوي الإعاقة يقل عمره عن 21 عامًا",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label:
			"يجب أن يكون عمر طفل واحد على الأقل من فئة ذوي الإعاقة أقل من 25 عامًا إذا كان نفس الطفل طالبًا مؤهلاً",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const ChildEligibilityforWomeninDifficulty = [
	{
		value: "662410003",
		label: "Not applicable",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410000",
		label: "At least 1 child less than 4 years old",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410001",
		label: "At least 1 PoD child less than 21 years old",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label: "At least 1 PoD child less than 25 years old if the same child is a qualified student",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const EducationCategoryCurriculumEn = [
	{
		value: "662410000",
		label: "Public Education",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label: "US",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410003",
		label: "UK",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410004",
		label: "IB",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410007",
		label: "Other",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const EducationCategoryCurriculumAr = [
	{
		value: "662410000",
		label: "المنهاج الوطني الإماراتي",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},

	{
		value: "662410002",
		label: " المنهاج الامريكي",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410003",
		label: "المنهاج البريطاني",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410004",
		label: "منهاج الباكالوريا الدولية",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410007",
		label: "آخر",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const PublicEducationStreamEn = [
	{
		value: "662410001",
		label: "Advanced Stream",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label: "Elite Stream",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410000",
		label: "General Stream",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const PublicEducationStreamAr = [
	{
		value: "662410001",
		label: "المسار المتقدم",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label: "مسار النخبة",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410000",
		label: "المسار العام",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const ScoresTypeDocumentEn = [
	{
		value: "662410001",
		label: "Advanced Placement",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410000",
		label: "EmSAT",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const ScoresTypeDocumentAr = [
	{
		value: "662410001",
		label: "اختبار المستوى المتقدم",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410000",
		label: "اختبار الإمارات القياسي",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const UtilityProviderEn = [
	{
		value: "662410001",
		label: "DEWA",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410003",
		label: "EWE",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label: "SEWA",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410004",
		label: "AADC (Al Ain Distribution Center)",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410005",
		label: "ADDC (Abu Dhabi Distribution Center)",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	// {
	// 	value: "662410000",
	// 	label: "TAQA",
	// 	RelatedId: "00000000-0000-0000-0000-000000000000",
	// },
];

export const UtilityProviderAr = [
	{
		value: "662410001",
		label: "(DEWA) هيئة كهرباء ومياه دبي",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410003",
		label: "(EWE) الاتحاد للماء والكهرباء",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410002",
		label: "(SEWA) هيئة كهرباء ومياه الشارقة",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	// {
	// 	value: "662410000",
	// 	label: "(TAQA) شركة أبو ظبي الوطنية للطاقة",
	// 	RelatedId: "00000000-0000-0000-0000-000000000000",
	// },
	{
		value: "662410004",
		label: "(AADC) شركة العين للتوزيع",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
	{
		value: "662410005",
		label: "(ADDC) شركة أبو ظبي للتوزيع",
		RelatedId: "00000000-0000-0000-0000-000000000000",
	},
];

export const CUSTOMER_PULSE_API = process.env.CUSTOMER_PULSE_API;
export const CUSTOMER_PULSE_SCRIPT_LINK = process.env.CUSTOMER_PULSE_SCRIPT_LINK;

//linking id for to whom customer pulse
export const CUSTOMER_PULSE_WHOM_LINKING_ID = process.env.CUSTOMER_PULSE_LINKING_ID;

// linking id for farmer and social aid
export const CUSTOMER_PULSE_AID_LINKING_ID = process.env.CUSTOMER_PULSE_AID_LINKING_ID;

export const CUSTOMER_PULSE_LINKING_ID = process.env.CUSTOMER_PULSE_LINKING_ID;

export const CUSTOMER_PULSE_GRANT_TYPE = process.env.CUSTOMER_PULSE_GRANT_TYPE;
export const CUSTOMER_PULSE_CLIENT_ID = process.env.CUSTOMER_PULSE_CLIENT_ID;
export const CUSTOMER_PULSE_CLIENT_SECRET = process.env.CUSTOMER_PULSE_CLIENT_SECRET;
export const CUSTOMER_PULSE_SCOPE = process.env.CUSTOMER_PULSE_SCOPE;

export const LOGO_LINK =
	"https://urldefense.com/v3/__http://www.mocd.gov.ae__;!!Nyu6ZXf5!qL8RNVK_joSbtmaYk7OCaWiWX88KHjRDEKFYKow1J7BOkD1Xb8DMDH2nKJNCmbp4pjS4PoBfZzbdIfnMZvQQYkyEg3Sar7o$";

export const STRAPI_API_URL = process.env.STRAPI_API_URL;
export const STRAPI_AUTH_TOKEN = process.env.STRAPI_AUTH_TOKEN;

export const GOOGLE_RECAPTCHA_API_URL = process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHA_API_URL;
